"""
Main FastAPI Application - CTI Dashboard API Router
"""

import asyncio
import logging
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NResponse
from pydantic import BaseModel

# Import our core modules
from .ioc_handler import <PERSON>o<PERSON>_Handler, IoC, DEFAULT_CONFIG as IOC_CONFIG
from .actor_summary_agent import ActorSummaryAgent, ThreatActor, ActorSummaryResponse, DEFAULT_CONFIG as ACTOR_CONFIG
from .passive_scan import PassiveScanner, PassiveScanResult, PassiveScanSummary
from .watchlist_monitor import (
    WatchlistMonitor, WatchlistItem, WatchlistAlert,
    WatchlistItemType, AlertSeverity, DEFAULT_CONFIG as WATCHLIST_CONFIG
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# FastAPI app instance
app = FastAPI(
    title="CTI Dashboard API",
    description="Cyber Threat Intelligence Dashboard - Comprehensive threat analysis and monitoring",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global configuration - In production, load from environment/config files
GLOBAL_CONFIG = {
    **IOC_CONFIG,
    **ACTOR_CONFIG,
    **WATCHLIST_CONFIG,
    'shodan_api_key': '',
    'censys_api_id': '',
    'censys_api_secret': '',
    'zoomeye_api_key': '',
    'enabled_sources': ['shodan', 'censys', 'zoomeye']
}

# Initialize core components
ioc_handler = IoC_Handler(GLOBAL_CONFIG)
actor_agent = ActorSummaryAgent(GLOBAL_CONFIG)
passive_scanner = PassiveScanner(GLOBAL_CONFIG)
watchlist_monitor = WatchlistMonitor(GLOBAL_CONFIG)

# Pydantic models for API requests/responses
class IoC_Request(BaseModel):
    value: str
    source: str
    threat_actor: Optional[str] = None
    malware_family: Optional[str] = None
    tags: List[str] = []

class IoC_BatchRequest(BaseModel):
    iocs: List[IoC_Request]

class ThreatActorRequest(BaseModel):
    name: str
    aliases: List[str] = []
    description: str
    origin_country: Optional[str] = None
    motivation: List[str] = []
    target_industries: List[str] = []
    target_regions: List[str] = []
    ttps: List[str] = []
    associated_malware: List[str] = []

class PassiveScanRequest(BaseModel):
    target: str
    scan_type: Optional[str] = 'auto'

class WatchlistItemRequest(BaseModel):
    value: str
    item_type: str
    description: str
    tags: List[str] = []
    severity: str = 'medium'
    expiry_days: Optional[int] = None

class AlertAcknowledgeRequest(BaseModel):
    acknowledged_by: str
    notes: str = ""

# API Routes

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "CTI Dashboard API",
        "version": "1.0.0",
        "endpoints": {
            "ioc": "/ioc/",
            "actor": "/actor/",
            "passive": "/passive/",
            "watchlist": "/watchlist/",
            "docs": "/docs"
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "components": {
            "ioc_handler": "operational",
            "actor_agent": "operational",
            "passive_scanner": "operational",
            "watchlist_monitor": "operational"
        }
    }

# IoC Management Endpoints
@app.post("/ioc/ingest", response_model=Dict[str, Any])
async def ingest_ioc(request: IoC_Request):
    """Ingest and enrich a single IoC"""
    try:
        enriched_ioc = await ioc_handler.ingest_ioc(
            value=request.value,
            source=request.source,
            threat_actor=request.threat_actor,
            malware_family=request.malware_family,
            tags=request.tags
        )

        return {
            "success": True,
            "ioc": {
                "value": enriched_ioc.value,
                "type": enriched_ioc.ioc_type,
                "confidence": enriched_ioc.confidence,
                "threat_actor": enriched_ioc.threat_actor,
                "malware_family": enriched_ioc.malware_family,
                "tags": enriched_ioc.tags,
                "enrichment_sources": list(enriched_ioc.enrichment_data.keys())
            }
        }

    except Exception as e:
        logger.error(f"IoC ingestion error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/ioc/batch", response_model=Dict[str, Any])
async def batch_ingest_iocs(request: IoC_BatchRequest):
    """Batch ingest multiple IoCs"""
    try:
        ioc_data = []
        for ioc_req in request.iocs:
            ioc_data.append({
                'value': ioc_req.value,
                'source': ioc_req.source,
                'threat_actor': ioc_req.threat_actor,
                'malware_family': ioc_req.malware_family,
                'tags': ioc_req.tags
            })

        enriched_iocs = await ioc_handler.batch_ingest(ioc_data)

        return {
            "success": True,
            "processed": len(enriched_iocs),
            "total_requested": len(request.iocs),
            "iocs": [
                {
                    "value": ioc.value,
                    "type": ioc.ioc_type,
                    "confidence": ioc.confidence
                } for ioc in enriched_iocs
            ]
        }

    except Exception as e:
        logger.error(f"Batch IoC ingestion error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Threat Actor Analysis Endpoints
@app.post("/actor/analyze", response_model=Dict[str, Any])
async def analyze_threat_actor(request: ThreatActorRequest):
    """Analyze threat actor and generate comprehensive summary"""
    try:
        # Create ThreatActor object
        actor = ThreatActor(
            name=request.name,
            aliases=request.aliases,
            description=request.description,
            first_seen=datetime.utcnow(),
            origin_country=request.origin_country,
            motivation=request.motivation,
            target_industries=request.target_industries,
            target_regions=request.target_regions,
            ttps=request.ttps,
            associated_malware=request.associated_malware
        )

        # Generate analysis
        summary = await actor_agent.analyze_threat_actor(actor)

        return {
            "success": True,
            "actor": {
                "name": actor.name,
                "aliases": actor.aliases
            },
            "analysis": {
                "executive_summary": summary.executive_summary,
                "attack_vectors": summary.attack_vectors,
                "target_analysis": summary.target_analysis,
                "mitre_techniques": summary.mitre_techniques,
                "risk_assessment": summary.risk_assessment,
                "recommendations": summary.recommendations,
                "confidence_level": summary.confidence_level
            }
        }

    except Exception as e:
        logger.error(f"Actor analysis error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/actor/report", response_model=Dict[str, Any])
async def generate_actor_report(request: ThreatActorRequest):
    """Generate formatted threat actor report"""
    try:
        # Create ThreatActor object
        actor = ThreatActor(
            name=request.name,
            aliases=request.aliases,
            description=request.description,
            first_seen=datetime.utcnow(),
            origin_country=request.origin_country,
            motivation=request.motivation,
            target_industries=request.target_industries,
            target_regions=request.target_regions,
            ttps=request.ttps,
            associated_malware=request.associated_malware
        )

        # Generate analysis and report
        summary = await actor_agent.analyze_threat_actor(actor)
        report = actor_agent.generate_actor_report(actor, summary)

        return {
            "success": True,
            "report": report
        }

    except Exception as e:
        logger.error(f"Actor report generation error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Passive Scanning Endpoints
@app.post("/passive/scan", response_model=Dict[str, Any])
async def passive_scan(request: PassiveScanRequest):
    """Perform passive scan on target"""
    try:
        scan_results = await passive_scanner.scan_target(
            target=request.target,
            scan_type=request.scan_type
        )

        # Aggregate results
        all_services = []
        all_vulnerabilities = []
        all_ports = set()
        sources = []

        for result in scan_results:
            all_services.extend(result.services)
            all_vulnerabilities.extend(result.vulnerabilities)
            all_ports.update(result.ports)
            sources.append(result.source)

        return {
            "success": True,
            "target": request.target,
            "scan_results": {
                "total_sources": len(scan_results),
                "sources": sources,
                "services_found": len(all_services),
                "vulnerabilities_found": len(all_vulnerabilities),
                "open_ports": sorted(list(all_ports)),
                "services": all_services[:10],  # Limit for response size
                "vulnerabilities": all_vulnerabilities[:10]
            }
        }

    except Exception as e:
        logger.error(f"Passive scan error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/passive/scan/{target}")
async def get_passive_scan_results(target: str):
    """Get cached passive scan results for target"""
    # This would typically query a database for cached results
    # For now, return a placeholder response
    return {
        "target": target,
        "cached_results": "Not implemented - would return cached scan data"
    }

# Watchlist Management Endpoints
@app.post("/watchlist/add", response_model=Dict[str, Any])
async def add_watchlist_item(request: WatchlistItemRequest):
    """Add item to watchlist"""
    try:
        # Convert string types to enums
        item_type = WatchlistItemType(request.item_type.lower())
        severity = AlertSeverity(request.severity.lower())

        # Calculate expiry date if specified
        expiry_date = None
        if request.expiry_days:
            from datetime import timedelta
            expiry_date = datetime.utcnow() + timedelta(days=request.expiry_days)

        # Create watchlist item
        item = WatchlistItem(
            id=str(uuid.uuid4()),
            value=request.value,
            item_type=item_type,
            description=request.description,
            added_date=datetime.utcnow(),
            added_by="api_user",  # In production, get from authentication
            tags=request.tags,
            severity=severity,
            expiry_date=expiry_date
        )

        success = watchlist_monitor.add_watchlist_item(item)

        return {
            "success": success,
            "item_id": item.id if success else None,
            "message": "Item added to watchlist" if success else "Failed to add item"
        }

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid input: {e}")
    except Exception as e:
        logger.error(f"Watchlist add error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/watchlist/{item_id}")
async def remove_watchlist_item(item_id: str):
    """Remove item from watchlist"""
    try:
        success = watchlist_monitor.remove_watchlist_item(item_id)

        return {
            "success": success,
            "message": "Item removed from watchlist" if success else "Item not found"
        }

    except Exception as e:
        logger.error(f"Watchlist remove error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/watchlist/items")
async def get_watchlist_items():
    """Get all active watchlist items"""
    try:
        items = watchlist_monitor.get_active_watchlist_items()

        return {
            "success": True,
            "total_items": len(items),
            "items": [
                {
                    "id": item.id,
                    "value": item.value,
                    "type": item.item_type.value,
                    "description": item.description,
                    "severity": item.severity.value,
                    "tags": item.tags,
                    "match_count": item.match_count,
                    "added_date": item.added_date.isoformat()
                } for item in items
            ]
        }

    except Exception as e:
        logger.error(f"Watchlist get items error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/watchlist/alerts")
async def get_watchlist_alerts(
    severity: Optional[str] = None,
    acknowledged: Optional[bool] = None,
    limit: int = 100
):
    """Get watchlist alerts with optional filtering"""
    try:
        severity_filter = None
        if severity:
            severity_filter = AlertSeverity(severity.lower())

        alerts = watchlist_monitor.get_alerts(
            severity_filter=severity_filter,
            acknowledged_filter=acknowledged,
            limit=limit
        )

        return {
            "success": True,
            "total_alerts": len(alerts),
            "alerts": [
                {
                    "id": alert.id,
                    "watchlist_item_id": alert.watchlist_item_id,
                    "matched_value": alert.matched_value,
                    "match_type": alert.match_type,
                    "source": alert.source,
                    "severity": alert.severity.value,
                    "timestamp": alert.timestamp.isoformat(),
                    "acknowledged": alert.acknowledged,
                    "acknowledged_by": alert.acknowledged_by,
                    "notes": alert.notes
                } for alert in alerts
            ]
        }

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid severity: {e}")
    except Exception as e:
        logger.error(f"Get alerts error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/watchlist/alerts/{alert_id}/acknowledge")
async def acknowledge_alert(alert_id: str, request: AlertAcknowledgeRequest):
    """Acknowledge a watchlist alert"""
    try:
        success = watchlist_monitor.acknowledge_alert(
            alert_id=alert_id,
            acknowledged_by=request.acknowledged_by,
            notes=request.notes
        )

        return {
            "success": success,
            "message": "Alert acknowledged" if success else "Alert not found"
        }

    except Exception as e:
        logger.error(f"Alert acknowledge error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/watchlist/check")
async def check_intelligence_against_watchlist(intel_data: Dict[str, Any]):
    """Check intelligence data against watchlist"""
    try:
        alerts = await watchlist_monitor.check_intelligence_data(intel_data)

        return {
            "success": True,
            "alerts_generated": len(alerts),
            "alerts": [
                {
                    "id": alert.id,
                    "matched_value": alert.matched_value,
                    "match_type": alert.match_type,
                    "severity": alert.severity.value
                } for alert in alerts
            ]
        }

    except Exception as e:
        logger.error(f"Watchlist check error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/watchlist/stats")
async def get_watchlist_statistics():
    """Get watchlist monitoring statistics"""
    try:
        stats = watchlist_monitor.get_watchlist_statistics()

        return {
            "success": True,
            "statistics": stats
        }

    except Exception as e:
        logger.error(f"Watchlist stats error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# System Management Endpoints
@app.get("/system/config")
async def get_system_config():
    """Get system configuration (sanitized)"""
    sanitized_config = {
        "enabled_sources": GLOBAL_CONFIG.get('enabled_sources', []),
        "llm_service": GLOBAL_CONFIG.get('llm_service', 'openai'),
        "enable_subnet_matching": GLOBAL_CONFIG.get('enable_subnet_matching', True)
    }

    return {
        "success": True,
        "config": sanitized_config
    }

@app.post("/system/config")
async def update_system_config(config_updates: Dict[str, Any]):
    """Update system configuration"""
    try:
        # In production, implement proper validation and persistence
        for key, value in config_updates.items():
            if key in ['enabled_sources', 'llm_service', 'enable_subnet_matching']:
                GLOBAL_CONFIG[key] = value

        return {
            "success": True,
            "message": "Configuration updated",
            "updated_keys": list(config_updates.keys())
        }

    except Exception as e:
        logger.error(f"Config update error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Background task for continuous monitoring
async def continuous_monitoring():
    """Background task for continuous threat monitoring"""
    while True:
        try:
            # This would typically:
            # 1. Pull new intelligence from feeds
            # 2. Check against watchlist
            # 3. Generate alerts
            # 4. Update statistics

            logger.info("Continuous monitoring cycle completed")
            await asyncio.sleep(300)  # Run every 5 minutes

        except Exception as e:
            logger.error(f"Continuous monitoring error: {e}")
            await asyncio.sleep(60)  # Wait 1 minute before retry

# Startup event
@app.on_event("startup")
async def startup_event():
    """Initialize application on startup"""
    logger.info("CTI Dashboard API starting up...")

    # Initialize components
    logger.info("Core components initialized")

    # Start background monitoring (commented out for demo)
    # asyncio.create_task(continuous_monitoring())

    logger.info("CTI Dashboard API ready")

# Shutdown event
@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on application shutdown"""
    logger.info("CTI Dashboard API shutting down...")

    # Cleanup resources
    logger.info("Cleanup completed")

# Error handlers
@app.exception_handler(404)
async def not_found_handler(request, exc):
    return JSONResponse(
        status_code=404,
        content={"error": "Endpoint not found", "detail": str(exc)}
    )

@app.exception_handler(500)
async def internal_error_handler(request, exc):
    return JSONResponse(
        status_code=500,
        content={"error": "Internal server error", "detail": "An unexpected error occurred"}
    )

if __name__ == "__main__":
    import uvicorn

    # Run the application
    uvicorn.run(
        "app:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )